<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>支付跳转</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            background: #fff;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            margin-top: 20px;
            color: #666;
            font-size: 14px;
        }
        
        .hidden-frame {
            position: absolute;
            left: -9999px;
            width: 1px;
            height: 1px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <div class="loading-text">正在处理支付请求...</div>
    </div>
    
    <iframe id="hiddenFrame" class="hidden-frame"></iframe>

    <script>
        // 强化版支付宝菜单隐藏
        function hideAlipayMenu() {
            function ready(callback) {
                if (window.AlipayJSBridge) {
                    callback && callback();
                } else {
                    document.addEventListener('AlipayJSBridgeReady', callback, false);
                }
            }

            ready(function() {
                try {
                    // 隐藏右上角菜单
                    AlipayJSBridge.call('hideOptionMenu');
                    
                    // 隐藏标题栏
                    AlipayJSBridge.call('hideToolbar');
                    
                    // 禁用分享
                    AlipayJSBridge.call('hideShareButton');
                    
                    // 设置标题栏
                    AlipayJSBridge.call('setToolbarColor', {
                        backgroundColor: '#ffffff',
                        color: '#000000'
                    });
                    
                    // 禁用右滑返回
                    AlipayJSBridge.call('setGestureBack', {
                        val: false
                    });
                    
                    console.log('支付宝环境配置完成');
                } catch(e) {
                    console.log('支付宝环境配置失败:', e);
                }
            });
            
            // 额外的DOM操作隐藏可能的菜单元素
            setTimeout(() => {
                const elements = document.querySelectorAll('[class*="menu"], [class*="option"], [class*="share"], [id*="menu"], [id*="option"]');
                elements.forEach(el => {
                    el.style.display = 'none';
                    el.style.visibility = 'hidden';
                });
            }, 100);
        }

        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 在iframe中加载Java接口，等待其跳转完成后获取最终URL
        function loadInHiddenFrame(url) {
            return new Promise((resolve) => {
                const iframe = document.getElementById('hiddenFrame');
                let finalUrl = url;
                let checkCount = 0;
                const maxChecks = 50; // 最多检查5秒

                iframe.onload = function() {
                    // 开始监控iframe的URL变化
                    const checkUrl = () => {
                        try {
                            const currentUrl = iframe.contentWindow.location.href;

                            // 如果URL包含爱奇艺支付页面，说明跳转完成
                            if (currentUrl.includes('vip.iqiyi.com') && currentUrl.includes('quickPayWrap')) {
                                finalUrl = currentUrl;
                                resolve(finalUrl);
                                return;
                            }

                            // 如果URL发生了变化但还不是最终页面，继续等待
                            if (currentUrl !== url && checkCount < maxChecks) {
                                checkCount++;
                                setTimeout(checkUrl, 100);
                            } else if (checkCount >= maxChecks) {
                                // 超时，返回当前URL
                                resolve(currentUrl);
                            } else {
                                setTimeout(checkUrl, 100);
                            }

                        } catch(e) {
                            // 跨域错误，说明已经跳转到其他域名
                            // 这时候我们无法获取URL，但可以确定跳转已经发生
                            checkCount++;
                            if (checkCount >= maxChecks) {
                                resolve(url); // 返回原URL，让其自然跳转
                            } else {
                                setTimeout(checkUrl, 100);
                            }
                        }
                    };

                    // 开始检查
                    setTimeout(checkUrl, 100);
                };

                iframe.onerror = function() {
                    resolve(url);
                };

                // 开始加载
                iframe.src = url;
            });
        }

        // 主逻辑
        async function init() {
            // 立即隐藏支付宝菜单
            hideAlipayMenu();
            
            const targetUrl = getUrlParameter('url');
            
            if (!targetUrl) {
                alert('缺少目标URL参数');
                return;
            }
            
            let finalUrl = decodeURIComponent(targetUrl);
            
            try {
                // 如果是中转链接，在隐藏iframe中处理
                if (finalUrl.includes('payapi.phone580.com/fzsh5pay/iqiyisignpay')) {
                    console.log('检测到Java接口中转链接，使用iframe处理...');

                    // 在隐藏iframe中加载，等待跳转完成
                    finalUrl = await loadInHiddenFrame(finalUrl);
                    console.log('iframe处理完成，准备跳转到:', finalUrl);
                }
                
                // 跳转到最终URL，并确保支付宝菜单隐藏生效
                setTimeout(() => {
                    window.location.href = finalUrl;
                }, 300);
                
            } catch(e) {
                console.error('处理失败:', e);
                // 失败时直接跳转原URL
                window.location.href = finalUrl;
            }
        }

        // 确保在各种环境下都能执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
        } else {
            init();
        }
        
        // 支付宝环境特殊处理
        document.addEventListener('AlipayJSBridgeReady', hideAlipayMenu, false);
        if (window.AlipayJSBridge) {
            hideAlipayMenu();
        }
    </script>
</body>
</html>
