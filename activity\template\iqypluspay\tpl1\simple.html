<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>支付跳转</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #fff;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            overflow: hidden;
        }
        
        .container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            margin-top: 20px;
            color: #666;
            font-size: 14px;
        }
        
        .hidden-frame {
            position: absolute;
            left: -9999px;
            width: 1px;
            height: 1px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <div class="loading-text">正在处理支付...</div>
    </div>
    
    <iframe id="hiddenFrame" class="hidden-frame"></iframe>

    <script>
        // 强化版支付宝菜单隐藏
        function hideAlipayMenu() {
            function ready(callback) {
                if (window.AlipayJSBridge) {
                    callback && callback();
                } else {
                    document.addEventListener('AlipayJSBridgeReady', callback, false);
                }
            }

            ready(function() {
                try {
                    AlipayJSBridge.call('hideOptionMenu');
                    AlipayJSBridge.call('hideToolbar');
                    AlipayJSBridge.call('hideShareButton');
                    AlipayJSBridge.call('setGestureBack', { val: false });
                    console.log('支付宝环境配置完成');
                } catch(e) {
                    console.log('支付宝环境配置失败:', e);
                }
            });
        }

        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 通过AJAX调用接口获取最终跳转URL
        async function getRedirectUrlFromApi(apiUrl) {
            try {
                const response = await fetch(apiUrl, {
                    method: 'GET',
                    redirect: 'manual', // 不自动跟随重定向
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15'
                    }
                });

                // 检查是否有重定向
                if (response.status >= 300 && response.status < 400) {
                    const location = response.headers.get('Location');
                    if (location) {
                        return location;
                    }
                }

                // 如果没有重定向，检查响应内容
                const text = await response.text();

                // 查找JavaScript重定向
                const jsRedirect = text.match(/window\.location\.href\s*=\s*['"]([^'"]+)['"]/);
                if (jsRedirect) {
                    return jsRedirect[1];
                }

                // 查找meta refresh
                const metaRedirect = text.match(/<meta[^>]+http-equiv=['"]refresh['"][^>]+content=['"][^;]*;\s*url=([^'"]+)['"]/i);
                if (metaRedirect) {
                    return metaRedirect[1];
                }

                return null;
            } catch (error) {
                console.error('获取重定向URL失败:', error);
                return null;
            }
        }

        // 主逻辑
        async function init() {
            // 立即隐藏支付宝菜单
            hideAlipayMenu();

            const targetUrl = getUrlParameter('url');

            if (!targetUrl) {
                alert('缺少目标URL参数');
                return;
            }

            let finalUrl = decodeURIComponent(targetUrl);

            // 如果是Java接口，通过AJAX获取最终URL
            if (finalUrl.includes('payapi.phone580.com/fzsh5pay/iqiyisignpay')) {
                console.log('检测到Java接口，通过AJAX获取最终URL...');

                const redirectUrl = await getRedirectUrlFromApi(finalUrl);
                if (redirectUrl) {
                    finalUrl = redirectUrl;
                    console.log('获取到最终URL:', finalUrl);
                } else {
                    console.log('无法获取最终URL，使用原URL');
                }
            }

            // 延迟跳转，确保支付宝菜单隐藏生效
            setTimeout(() => {
                window.location.href = finalUrl;
            }, 800);
        }

        // 确保在各种环境下都能执行
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
        } else {
            init();
        }
        
        // 支付宝环境特殊处理
        document.addEventListener('AlipayJSBridgeReady', function() {
            hideAlipayMenu();
            // 再次延迟确保生效
            setTimeout(init, 200);
        }, false);
        
        if (window.AlipayJSBridge) {
            hideAlipayMenu();
        }
    </script>
</body>
</html>
