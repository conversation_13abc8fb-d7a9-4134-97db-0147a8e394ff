<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>支付页面</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: #fff;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            z-index: 9999;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            margin-top: 20px;
            color: #666;
            font-size: 14px;
        }
        
        .error {
            display: none;
            text-align: center;
            padding: 50px 20px;
            color: #ff4d4f;
        }
        
        .iframe-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            display: none;
        }
        
        iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
    </style>
</head>
<body>
    <div class="loading" id="loading">
        <div class="spinner"></div>
        <div class="loading-text">正在跳转...</div>
    </div>
    
    <div class="error" id="error">
        <h3>页面加载失败</h3>
        <p>请检查链接是否正确</p>
    </div>
    
    <div class="iframe-container" id="iframeContainer">
        <iframe id="payFrame" src=""></iframe>
    </div>

    <script>
        // 隐藏支付宝右上角菜单
        function hideAlipayMenu() {
            function ready(callback) {
                if (window.AlipayJSBridge) {
                    callback && callback();
                } else {
                    document.addEventListener('AlipayJSBridgeReady', callback, false);
                }
            }

            ready(function() {
                try {
                    // 隐藏右上角菜单
                    AlipayJSBridge.call('hideOptionMenu');
                    
                    // 隐藏标题栏
                    AlipayJSBridge.call('hideToolbar');
                    
                    // 设置标题栏透明
                    AlipayJSBridge.call('setToolbarColor', {
                        backgroundColor: 'transparent'
                    });
                    
                    console.log('支付宝菜单已隐藏');
                } catch(e) {
                    console.log('非支付宝环境或隐藏失败:', e);
                }
            });
        }

        // 获取URL参数
        function getUrlParameter(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }

        // 解码URL
        function decodeUrl(url) {
            try {
                return decodeURIComponent(url);
            } catch(e) {
                console.error('URL解码失败:', e);
                return url;
            }
        }

        // 检查是否是中转链接
        function isRedirectUrl(url) {
            return url.includes('payapi.phone580.com/fzsh5pay/iqiyisignpay');
        }

        // 处理中转链接
        async function handleRedirectUrl(url) {
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    redirect: 'manual'
                });
                
                if (response.status === 302 || response.status === 301) {
                    const location = response.headers.get('Location');
                    if (location) {
                        return location;
                    }
                }
                
                // 如果没有重定向，尝试解析页面内容
                const text = await response.text();
                const match = text.match(/window\.location\.href\s*=\s*['"]([^'"]+)['"]/);
                if (match) {
                    return match[1];
                }
                
                // 尝试查找meta refresh
                const metaMatch = text.match(/<meta[^>]+http-equiv=['"]refresh['"][^>]+content=['"][^;]*;\s*url=([^'"]+)['"]/i);
                if (metaMatch) {
                    return metaMatch[1];
                }
                
                return url;
            } catch(e) {
                console.error('处理中转链接失败:', e);
                return url;
            }
        }

        // 主要逻辑
        async function init() {
            // 立即隐藏支付宝菜单
            hideAlipayMenu();
            
            const targetUrl = getUrlParameter('url');
            
            if (!targetUrl) {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'block';
                return;
            }
            
            let finalUrl = decodeUrl(targetUrl);
            
            // 如果是中转链接，先获取最终URL
            if (isRedirectUrl(finalUrl)) {
                console.log('检测到中转链接，正在获取最终URL...');
                finalUrl = await handleRedirectUrl(finalUrl);
                console.log('最终URL:', finalUrl);
            }
            
            // 直接跳转而不使用iframe
            setTimeout(() => {
                window.location.href = finalUrl;
            }, 500);
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', init);
        
        // 确保在支付宝环境中也能正常工作
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', init);
        } else {
            init();
        }
    </script>
</body>
</html>
