<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>一次性访问页面</title>
</head>
<body>
    <div class="container">
        <h1>专属内容页面</h1>
        <p>此链接仅可访问一次</p>
        <a href="https://www.baidu.com">返回首页</a>
        <div id="content">
            <!-- 这里放你的重要内容 -->
        </div>
    </div>

    <script>
        // 隐藏支付宝右上角菜单
        function ready(callback) {
            if (window.AlipayJSBridge) {
                callback && callback();
            } else {
                document.addEventListener('AlipayJSBridgeReady', callback, false);
            }
        }

        ready(function() {
            try {
                AlipayJSBridge.call('hideOptionMenu');
            } catch(e) {
                console.log('非支付宝环境');
            }
        });
    </script>
</body>
</html>